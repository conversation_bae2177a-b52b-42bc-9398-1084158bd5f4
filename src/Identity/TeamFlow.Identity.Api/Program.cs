using System.IdentityModel.Tokens.Jwt;
using System.Reflection;
using TeamFlow.Identity.Api.Auth;
using TeamFlow.Identity.Api.Extensions;
using TeamFlow.Identity.Api.Utils;
using TeamFlow.Identity.Api.Utils.Interfaces;
using TeamFlow.Identity.Application.Services;
using TeamFlow.Identity.Application.Services.Interfaces;
using FluentValidation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using TeamFlow.Identity.Api.Grpc.User;
using TeamFlow.Identity.Application.Contracts.Internal;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Internal;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Identity.Persistence.Database;
using TeamFlow.Identity.Persistence.Repositories;

//TODO Разобраться че за хуйня с gRPC. Как без TLS запустить спокойно

/*
 * "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:5019",
        "Protocols": "Http1AndHttp2"
      },
      "Grpc": {
        "Url": "http://localhost:5025",
        "Protocols": "Http2"
      }
    }
  },
 */
var builder = WebApplication.CreateBuilder(args);

// builder.WebHost.ConfigureKestrel(options =>
// {
//     // HTTP/1 и HTTP/2 без TLS для разработки
//     options.ListenLocalhost(5019, listenOptions =>
//     {
//         listenOptions.Protocols = Microsoft.AspNetCore.Server.Kestrel.Core.HttpProtocols.Http1AndHttp2;
//     });
//
//     // Отдельный порт для gRPC
//     options.ListenLocalhost(5025, listenOptions =>
//     {
//         listenOptions.Protocols = Microsoft.AspNetCore.Server.Kestrel.Core.HttpProtocols.Http2;
//     });
// });

builder.Host.UseElasticsearchSerilog();

//TODO Вынести все реги в extensions.
//TODO Докрутить кеширование
//TODO Удалить лишние зависимости
//TODO Пересмотреть структуру директорий

// Add services to the container.

builder.Services.AddControllers();

// builder.Services.AddGrpc(options =>
// {
//     // Настройки для gRPC (опционально)
//     options.MaxReceiveMessageSize = 4 * 1024 * 1024; // 4MB
//     options.MaxSendMessageSize = 4 * 1024 * 1024; // 4MB
//     options.EnableDetailedErrors = builder.Environment.IsDevelopment();
// });

// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var configuration = builder.Configuration;

builder.Services.AddScoped<IHashDataService>(_ => new HashDataService(9));

var connectionString = configuration.GetSection("Database").GetValue<string>("DefaultConnection");

builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(connectionString)
);

builder.Services.AddSingleton(new AuthOptions
{
    Issuer = configuration.GetSection("Auth").GetValue<string>("Issuer"),
    Audience = configuration.GetSection("Auth").GetValue<string>("Audience"),
    Key = configuration.GetSection("Auth").GetValue<string>("Key"),
    AccessTokenLifetime = configuration.GetSection("Auth").GetSection("TokenExpiration").GetValue<int>("Access"),
    RefreshTokenLifetime = configuration.GetSection("Auth").GetSection("TokenExpiration").GetValue<int>("Refresh")
});

builder.Services.AddSingleton<AuthConfiguration>();

builder.Services.AddSingleton<JwtSecurityTokenHandler>();
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<ICookieUtils, CookieUtils>();

builder.Services.AddValidatorsFromAssembly(Assembly.GetAssembly(typeof(IApplicationAssemblyMarker)));

builder.Services.AddScoped<IGenericRepository<User, Guid>, GenericRepository<User, Guid>>();
builder.Services.AddScoped<IGenericRepository<Position, Guid>, GenericRepository<Position, Guid>>();

builder.Services.AddScoped<IRefreshTokenHelper, RefreshTokenHelper>();

builder.Services.AddMediatR(cfg =>
{
    cfg.RegisterServicesFromAssemblies(Assembly.GetAssembly(typeof(IApplicationAssemblyMarker))!,
        Assembly.GetAssembly(typeof(IApplicationContractsAssemblyMarker))!);
});

// builder.Services.AddScoped<GetUserByGuidGrpcService>();
// builder.Services.AddScoped<GetUsersPagedGrpcService>();

builder.Services.AddSwaggerGen(options =>
{
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        In = ParameterLocation.Header,
        Description = "Please enter a valid token",
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        BearerFormat = "JWT",
        Scheme = "bearer"
    });

    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            []
        }
    });
});

builder.Services
    .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        using var serviceProvider = builder.Services.BuildServiceProvider();
        var authConfig = serviceProvider.GetRequiredService<AuthConfiguration>();

        options.TokenValidationParameters = authConfig.GetTokenValidationParameters();
    });

builder.Services.AddHealthChecks();

var app = builder.Build();


app.UseCorsSettings();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    // app.MapOpenApi();
    app.UseSwagger();
    // app.UseSwaggerUI(options =>
    // {
    //     options.SwaggerEndpoint("/openapi/v1.json", "VanId TeamFlow.Identity.Api");
    // });
    app.UseSwaggerUI();
}

app.MapHealthChecks("/healthz");

app.UseHttpsRedirection();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// app.MapGrpcService<GetUserByGuidGrpcService>();
// app.MapGrpcService<GetUsersPagedGrpcService>();

app.Run();