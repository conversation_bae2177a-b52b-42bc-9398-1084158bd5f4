using Microsoft.AspNetCore.Mvc;
using TeamFlow.Identity.Core.Enums;
using TeamFlow.Identity.Core.Enums.Users;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Api.Api.Filtering.Users;

public record UserFilteringQueryParameters(
    [property: FromQuery(Name = "login")] string? Login,
    [property: FromQuery(Name = "email")] string? Email,
    [property: FromQuery(Name = "onlineStatus")] UserOnlineStatus? OnlineStatus,
    [property: FromQuery(Name = "role")] UserRole? Role,
    [property: FromQuery(Name = "positionId")] Guid? PositionId);