using Microsoft.EntityFrameworkCore.Storage;
using TeamFlow.Shared.Repositories.Repositories.Common;

namespace TeamFlow.Identity.Persistence.Database;

public class Transaction : ITransaction
{
    private readonly IDbContextTransaction _dbContextTransaction;
    private bool _disposed = false;

    public Transaction(IDbContextTransaction dbContextTransaction)
    {
        ArgumentNullException.ThrowIfNull(dbContextTransaction);
        _dbContextTransaction = dbContextTransaction;
    }

    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        await _dbContextTransaction.CommitAsync(cancellationToken);
    }

    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        await _dbContextTransaction.RollbackAsync(cancellationToken);
    }

    public void Dispose()
    {
        if (_disposed) return;
        _dbContextTransaction.Dispose();
        _disposed = true;
    }

    public async ValueTask DisposeAsync()
    {
        if (!_disposed)
        {
            await _dbContextTransaction.DisposeAsync();
            _disposed = true;
        }
    }
    
    private void ThrowIfDisposed()
    {
        if (!_disposed) return;
        throw new ObjectDisposedException(nameof(Transaction));
    }
}