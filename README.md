# TeamFlow.Task

## Описание проекта

TeamFlow.Task - это микросервис для управления задачами и проектами в рамках командной работы. Система позволяет
создавать проекты, формировать команды, назначать задачи и отслеживать их выполнение.

## Архитектура

Проект следует принципам Clean Architecture и состоит из следующих основных компонентов:

- TeamFlow.Task.Core : Содержит бизнес-модели, интерфейсы и абстракции
- TeamFlow.Task.Application : Содержит бизнес-логику и сервисы
- TeamFlow.Task.Infrastructure : Содержит реализации инфраструктурных сервисов (БД, внешние API)
- TeamFlow.Task.Api : Содержит API-контроллеры и конфигурацию приложения

### Зависимости между проектами

- Core не должен зависеть от других проектов
- Application может зависеть только от Core
- Infrastructure может зависеть от Core и Application
- Api может зависеть от всех проектов

## Основные возможности

- Управление проектами (создание, редактирование, удаление)
- Управление командами (создание, назначение участников, назначение тимлида)
- Управление задачами (создание, назначение исполнителей, отслеживание статуса)
- Канбан-доска для визуализации рабочего процесса
- Приоритизация задач и проектов
- Отслеживание времени выполнения задач

## Технологии

- .NET 9.0
- ASP.NET Core
- Entity Framework Core
- MongoDB
- gRPC для межсервисного взаимодействия
- FluentValidation для валидации данных
- FluentResults для обработки результатов операций
- Serilog для логирования
- Docker для контейнеризации

## Запуск проекта

### Требования

- .NET 9.0 SDK
- Runtime (aspnetcore-runtime)
- Docker
- Docker Compose

### Установка требуемых пакетов

Если вы **линуксоид**, то вы собственно без проблем подменить apt на ваш пакетный менеджер, и найдете какие репозитории
нужно вам добавить.

Поэтому оставляю примеры только для дистрибутивов использующих apt

```bash
sudo apt-get update && \
sudo apt-get install -y dotnet-sdk-9.0
```

```bash
sudo apt-get update && \
sudo apt-get install -y aspnetcore-runtime-9.0
```

Если вы windows enjoyer, то качайте **[.NET](https://dotnet.microsoft.com/en-us/download)** с офф сайта или через winget

MacOS братья, качайте тоже с **[офф сайта](https://dotnet.microsoft.com/en-us/download)** или через brew

### Локальный запуск

1. Клонировать репозиторий:

    ```bash
    git clone https://github.com/your-repo/TeamFlow.Task.git
    cd TeamFlow.Task 
    ```
2. Настроить переменные окружения (скопировать [.env.example](.env.example) в .env и отредактировать при необходимости)
3. Запустить с помощью Docker Compose:
    ```bash
   docker-compose up -d
    ```
4. [Применить миграции](#применение-миграций)
5. Или запустить локально:
    ```bash
    dotnet restore
    dotnet build
    cd src/TeamFlow.Task.Api
    dotnet run
    ```

## Стиль кода

Проект следует конвенции стиля кода, описанной в [документации](./docs/code-style-convention.md).

## Применение миграций {#применение-миграций}

Скрипты поддерживают работу с миграциями для разных проектов в решении. Доступные контексты:
- Tasks (TeamFlow.Tasks.Infrastructure)
- Identity (TeamFlow.Identity.Persistence)
- Gateway (TeamFlow.Gateway.Infrastructure)

Можно использовать встроенные команды. Например:

```bash
   dotnet ef migrations add ExtendUserSettingsAndSkills --project src/Identity/TeamFlow.Identity.Persistence
```

```ps1
   dotnet ef migrations add ExtendUserSettingsAndSkills --project src/Identity/TeamFlow.Identity.Persistence
```

### PowerShell
```ps1
# Добавить новую миграцию (используя контекст)
.\scripts\migrations.ps1 -Command add -Name "AddUserTable" -Context Tasks

# Применить миграции (используя контекст)
.\scripts\migrations.ps1 -Command update -Context Tasks

# Удалить последнюю миграцию (используя контекст)
.\scripts\migrations.ps1 -Command remove -Context Tasks

# Использовать конкретный проект напрямую
.\scripts\migrations.ps1 -Command add -Name "AddUserTable" -Project "TeamFlow.Identity.Infrastructure"
```

### Bash
```bash
# Сделать скрипт исполняемым
chmod +x scripts/migrations.sh

# Добавить новую миграцию (используя контекст)
./scripts/migrations.sh -c Tasks add AddUserTable

# Применить миграции (используя контекст)
./scripts/migrations.sh -c Tasks update

# Удалить последнюю миграцию (используя контекст)
./scripts/migrations.sh -c Tasks remove

# Использовать конкретный проект напрямую
./scripts/migrations.sh -p TeamFlow.Identity.Infrastructure add AddUserTable
```

## Доступные руководства

- [Асинхронное программирование](./docs/async-programming-guide.md)
- [Внутрення библиотека `ExpressionBuilder`](./docs/expression-builder-guide.md)
- [Руководство по стилю кода](./docs/code-style-convention.md)
