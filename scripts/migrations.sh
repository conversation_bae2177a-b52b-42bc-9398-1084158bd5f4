#!/bin/bash

# Определение проектов с миграциями
declare -A MIGRATION_PROJECTS=(
    ["Tasks"]="TeamFlow.Tasks.Infrastructure"
    ["Identity"]="TeamFlow.Identity.Infrastructure"
    ["Gateway"]="TeamFlow.Gateway.Infrastructure"
)

# Function to display usage
usage() {
    echo "Usage: $0 <command> [options]"
    echo "Commands:"
    echo "  add <name>    - Add a new migration"
    echo "  update        - Apply migrations to database"
    echo "  remove        - Remove the last migration"
    echo "Options:"
    echo "  -c, --context - Specify context (Tasks, Identity, Gateway)"
    echo "  -p, --project - Specify project path directly"
    echo "Available contexts:"
    for key in "${!MIGRATION_PROJECTS[@]}"; do
        echo "  $key -> ${MIGRATION_PROJECTS[$key]}"
    done
    exit 1
}

# Function to get project path
get_project_path() {
    local context=$1
    local project=$2

    if [ ! -z "$project" ]; then
        echo "$project"
        return
    fi

    if [ ! -z "$context" ] && [ ! -z "${MIGRATION_PROJECTS[$context]}" ]; then
        echo "${MIGRATION_PROJECTS[$context]}"
        return
    fi

    echo "Error: Context '$context' not found" >&2
    usage
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        add)
            COMMAND="add"
            if [[ -z $2 ]]; then
                echo "Error: Migration name is required for 'add' command"
                usage
            fi
            NAME="$2"
            shift 2
            ;;
        update)
            COMMAND="update"
            shift
            ;;
        remove)
            COMMAND="remove"
            shift
            ;;
        -c|--context)
            CONTEXT="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Get project path
PROJECT_PATH=$(get_project_path "$CONTEXT" "$PROJECT")

# Execute command
case $COMMAND in
    add)
        echo "Adding new migration: $NAME to project: $PROJECT_PATH"
        dotnet ef migrations add "$NAME" --project "$PROJECT_PATH"
        ;;
    update)
        echo "Applying migrations to database for project: $PROJECT_PATH"
        dotnet ef database update --project "$PROJECT_PATH"
        ;;
    remove)
        echo "Removing last migration from project: $PROJECT_PATH"
        dotnet ef migrations remove --project "$PROJECT_PATH"
        ;;
    *)
        usage
        ;;
esac 